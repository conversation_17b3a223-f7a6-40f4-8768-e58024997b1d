import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { API_CONFIG, enhancedFetch, getNetworkErrorMessage, isNetworkError } from '../../utils/networkUtils';

// Types
export interface AuthState {
  user: {
    id?: string;
    username?: string;
    email?: string;
    phone?: string;
    isFirstLogin?: boolean;
  } | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  otpData: {
    email?: string;
    phone?: string;
    method?: 'email' | 'phone' | 'whatsapp';
    attempts: number;
    lastAttempt?: Date;
  } | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  otpData: null,
};

// Helper function to retry failed requests
const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries = API_CONFIG.RETRY_ATTEMPTS,
  delay = API_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      console.log(`Request attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError!;
};

// Async thunks
export const checkUserStatus = createAsyncThunk(
  'auth/checkUserStatus',
  async (authenticator: string) => {
    console.log('Making API call to:', `${API_CONFIG.BASE_URL}/user/check_status/`);
    console.log('Request payload:', { authenticator });
    
    return retryRequest(async () => {
      try {
        console.log('Starting fetch request...');
        const response = await enhancedFetch(
          `${API_CONFIG.BASE_URL}/user/check_status/`,
          {
            method: 'POST',
            body: JSON.stringify({ authenticator }),
          },
          API_CONFIG.TIMEOUT
        );
        
        console.log('Response received!');
        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.log('Error response:', errorData);
          throw new Error(errorData.error || `Server error: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Success response:', data);
        return data;
      } catch (error) {
        console.log('Fetch error occurred:');
        console.log('Error type:', typeof error);
        console.log('Error message:', error instanceof Error ? error.message : 'Unknown error');
        console.log('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
        
        const errorMessage = getNetworkErrorMessage(error);
        throw new Error(errorMessage);
      }
    });
  }
);

export const requestOTP = createAsyncThunk(
  'auth/requestOTP',
  async ({ email, method }: { email: string; method: 'email' | 'phone' | 'whatsapp' }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/forgot_password/request_otp/`,
        {
          method: 'POST',
          body: JSON.stringify({ email, method }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to request OTP');
      }
      
      return response.json();
    });
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ email, otp }: { email: string; otp: string }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/forgot_password/verify_otp/`,
        {
          method: 'POST',
          body: JSON.stringify({ email, otp }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Invalid OTP');
      }
      
      return response.json();
    });
  }
);

export const setNewPassword = createAsyncThunk(
  'auth/setNewPassword',
  async ({ email, newPassword }: { email: string; newPassword: string }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/forgot_password/set_new_password/`,
        {
          method: 'POST',
          body: JSON.stringify({ email, new_password: newPassword }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to set new password');
      }
      
      return response.json();
    });
  }
);

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { username: string; password?: string }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/login/`,
        {
          method: 'POST',
          body: JSON.stringify(credentials),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Login failed');
      }
      
      return response.json();
    });
  }
);

export const setPassword = createAsyncThunk(
  'auth/setPassword',
  async ({ userId, oldPassword, newPassword }: { 
    userId: string; 
    oldPassword: string; 
    newPassword: string; 
  }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/set_password/`,
        {
          method: 'POST',
          body: JSON.stringify({ user_id: userId, old_password: oldPassword, new_password: newPassword }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to set password');
      }
      
      return response.json();
    });
  }
);

export const resendOTP = createAsyncThunk(
  'auth/resendOTP',
  async ({ email, method }: { email: string; method: 'email' | 'phone' | 'whatsapp' }) => {
    return retryRequest(async () => {
      const response = await enhancedFetch(
        `${API_CONFIG.BASE_URL}/user/forgot_password/resend_otp/`,
        {
          method: 'POST',
          body: JSON.stringify({ email, method }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to resend OTP');
      }
      
      return response.json();
    });
  }
);

// Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setOTPData: (state, action: PayloadAction<AuthState['otpData']>) => {
      state.otpData = action.payload;
    },
    incrementOTPAttempts: (state) => {
      if (state.otpData) {
        state.otpData.attempts += 1;
        state.otpData.lastAttempt = new Date();
      }
    },
    resetOTPData: (state) => {
      state.otpData = null;
    },
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.otpData = null;
    },
  },
  extraReducers: (builder) => {
    // Check user status
    builder
      .addCase(checkUserStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkUserStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(checkUserStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to check user status';
      });

    // Request OTP
    builder
      .addCase(requestOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(requestOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.otpData = {
          email: action.meta.arg.email,
          method: action.meta.arg.method,
          attempts: 0,
          lastAttempt: new Date(),
        };
      })
      .addCase(requestOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to request OTP';
      });

    // Verify OTP
    builder
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Invalid OTP';
      });

    // Set new password
    builder
      .addCase(setNewPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setNewPassword.fulfilled, (state) => {
        state.isLoading = false;
        state.otpData = null;
      })
      .addCase(setNewPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to set new password';
      });

    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      });

    // Set password
    builder
      .addCase(setPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user) {
          state.user.isFirstLogin = false;
        }
      })
      .addCase(setPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to set password';
      });

    // Resend OTP
    builder
      .addCase(resendOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.otpData) {
          state.otpData.attempts = 0;
          state.otpData.lastAttempt = new Date();
        }
      })
      .addCase(resendOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to resend OTP';
      });
  },
});

export const {
  setLoading,
  setError,
  clearError,
  setOTPData,
  incrementOTPAttempts,
  resetOTPData,
  logout,
} = authSlice.actions;

export default authSlice.reducer; 