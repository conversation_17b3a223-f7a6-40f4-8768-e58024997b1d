// Network utility functions
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    // For now, we'll use a simple fetch test to check connectivity
    const response = await fetch('https://www.google.com', { 
      method: 'HEAD'
    });
    return response.ok;
  } catch (error) {
    console.log('Network connectivity check failed:', error);
    return false;
  }
};

export const getNetworkErrorMessage = (error: any): string => {
  if (error.name === 'AbortError' || error.message === 'Aborted') {
    return 'Request timed out. Please check your internet connection and try again.';
  }
  
  if (error.message?.includes('Network request failed')) {
    return 'Network error. Please check your internet connection and try again.';
  }
  
  if (error.message?.includes('fetch')) {
    return 'Unable to connect to server. Please try again later.';
  }
  
  if (error.message?.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
};

export const isNetworkError = (error: any): boolean => {
  return (
    error.name === 'AbortError' ||
    error.message?.includes('Network request failed') ||
    error.message?.includes('fetch') ||
    error.message?.includes('timeout') ||
    error.message?.includes('network') ||
    error.message?.includes('connection')
  );
};

// API configuration
export const API_CONFIG = {
  BASE_URL: 'http://192.168.0.185:8000',
  TIMEOUT: 15000, // 15 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
};

// Enhanced fetch with better error handling
export const enhancedFetch = async (
  url: string, 
  options: RequestInit = {}, 
  timeout = API_CONFIG.TIMEOUT
): Promise<Response> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}; 