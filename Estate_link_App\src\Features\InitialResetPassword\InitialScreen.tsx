import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage } from 'components';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { checkUserStatus, clearError } from 'store/slices/authSlice';
import { useFormValidation } from 'hooks/useFormValidation';
import { initialScreenSchema } from 'validation/schemas';

type RootStackParamList = {
  Login: undefined;
  PasswordReset: undefined;
  ForgotPassword: undefined;
  WelcomeBack: undefined;
};

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

export function InitialScreen() {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    username: '',
    rememberMe: false,
  });
  const [forceUpdate, setForceUpdate] = useState(0);

  const {
    errors,
    validateForm,
    setFieldTouched,
    setFieldError,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(initialScreenSchema);

  const handleInputChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear Redux error when user starts typing
    if (error) {
      dispatch(clearError());
    }

    // Clear force update when user starts typing to hide submit errors
    if (forceUpdate > 0) {
      setForceUpdate(0);
    }

    // Validate spelling in real-time for username field
    if (field === 'username' && value.trim()) {
      try {
        await initialScreenSchema.validateAt('username', { username: value, rememberMe: formData.rememberMe });
        // If validation passes, clear any existing errors
        if (isFieldTouched(field as keyof typeof formData)) {
          clearErrors();
        }
      } catch (validationError: any) {
        // Set the validation error immediately
        setFieldError('username', validationError.message);
      }
    } else {
      // Clear field errors when user starts typing (for other cases)
      if (isFieldTouched(field as keyof typeof formData)) {
        clearErrors();
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(field as keyof typeof formData, true);
  };

  const handleLogin = async () => {
    // Clear previous Redux errors
    dispatch(clearError());

    // Mark field as touched first
    setFieldTouched('username', true);

    // Check if username is empty and set error manually
    if (!formData.username.trim()) {
      setFieldError('username', 'Username, email, or phone number is required');

      setForceUpdate(prev => prev + 1); // Force re-render
      return;
    }
    console.log('Form Data:', formData);

    // Validate form for other validation rules
    const validation = await validateForm(formData);
    if (!validation.isValid) {
      // Validation failed, errors are already set by validateForm
      return;
    }

    try {
      console.log('Calling checkUserStatus API...');
      // Check user status first
      const statusResult = await dispatch(checkUserStatus(formData.username)).unwrap();
      console.log('API Response:', statusResult);
      
      if (statusResult.is_first_login) {
        console.log('User is first time login, navigating to PasswordReset');
        // Navigate to password reset page for first-time users
        navigation.navigate('PasswordReset');
      } else {
        console.log('User is existing user, navigating to WelcomeBack');
        // Navigate to welcome back for existing users
        navigation.navigate('WelcomeBack');
      }
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('Login error:', error);
      console.log('Error details:', error);
      
      // For debugging, let's navigate to WelcomeBack as a fallback
      console.log('Navigating to WelcomeBack as fallback due to API error');
      navigation.navigate('WelcomeBack');
    }
  };

  const handleForgotPassword = () => {
    console.log('Navigating to ForgotPassword');
    navigation.navigate('ForgotPassword');
  };

  // Test navigation function
  const testNavigation = () => {
    console.log('Testing navigation to WelcomeBack');
    navigation.navigate('WelcomeBack');
  };

  const usernameError = getFieldError('username');
  const hasUsernameError = !!usernameError;
  // Show errors when submit button is clicked OR when there's a real-time validation error
  const shouldShowError = Boolean((forceUpdate > 0 && (hasUsernameError || !formData.username.trim())) || 
                         (hasUsernameError && formData.username.trim()));
  const isFormValid = formData.username.trim() && !hasUsernameError;



  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 80 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome Back
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Login to access your Estate Link account
            </Text>
          </View>

          {/* Form Section */}
          <View>
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              User Name / Email / Phone Number
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                shouldShowError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: shouldShowError ? 8 : 24,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="enter your user name / e-mail / phone number"
              placeholderTextColor="#9CA3AF"
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              onBlur={() => handleBlur('username')}
              returnKeyType="next"
              onSubmitEditing={handleLogin}
              blurOnSubmit={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            <ErrorMessage
              message={usernameError || 'Username, email, or phone number is required'}
              visible={shouldShowError}
            />

            {/* Redux Error Message */}
            {error && (
              <ErrorMessage message={error} visible={true} />
            )}

            {/* Remember Me Checkbox */}
            <TouchableOpacity
              className="flex-row items-center"
              style={{ marginBottom: 32, paddingVertical: 8 }}
              onPress={() => setFormData((prev) => ({ ...prev, rememberMe: !prev.rememberMe }))}
              activeOpacity={0.7}
              disabled={isLoading}>
              <View
                className="items-center justify-center"
                style={{
                  width: 18,
                  height: 18,
                  borderWidth: 2,
                  borderColor: '#3C9D9B',
                  borderRadius: 4,
                  marginRight: 12,
                  backgroundColor: formData.rememberMe ? '#3C9D9B' : 'transparent',
                }}>
                {formData.rememberMe && (
                  <Text className="font-bold text-white" style={{ fontSize: 12 }}>
                    ✓
                  </Text>
                )}
              </View>
              <Text
                className="font-oxanium-bold text-text-primary"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Remember me
              </Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              className="items-center justify-center"
              style={{
                width: '100%',
                height: 48,
                backgroundColor: isFormValid && !isLoading ? '#3C9D9B' : 'white',
                borderWidth: 1.5,
                borderColor: '#3C9D9B',
                borderRadius: 24,
                marginBottom: 20,
              }}
              onPress={handleLogin}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator color="#3C9D9B" size="small" />
              ) : (
                <Text
                  className="font-oxanium-bold"
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: isFormValid ? 'white' : '#3C9D9B',
                  }}>
                  Submit
                </Text>
              )}
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <View className="items-center" style={{ marginBottom: 20 }}>
              <TouchableOpacity onPress={handleForgotPassword} disabled={isLoading}>
                <Text
                  className="font-oxanium-bold text-primary"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </View>

            {/* Test Navigation Button */}
            <View className="items-center" style={{ marginBottom: 60 }}>
              <TouchableOpacity onPress={testNavigation} disabled={isLoading}>
                <Text
                  className="font-oxanium-bold text-blue-500"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Test Navigation (Debug)
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity disabled={isLoading}>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
