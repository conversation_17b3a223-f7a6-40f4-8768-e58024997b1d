import { API_CONFIG, enhancedFetch } from './networkUtils';

export const testBackendConnection = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log('Testing backend connection...');
    console.log('Backend URL:', API_CONFIG.BASE_URL);

    // First test with a simple HEAD request to check if server is reachable
    console.log('Testing server reachability...');
    const headResponse = await enhancedFetch(
      `${API_CONFIG.BASE_URL}/user/check_status/`,
      {
        method: 'HEAD',
      },
      10000 // 10 second timeout for testing
    );

    console.log('Server is reachable, testing API endpoint...');

    // Test actual API endpoint
    const response = await enhancedFetch(
      `${API_CONFIG.BASE_URL}/user/check_status/`,
      {
        method: 'POST',
        body: JSON.stringify({ authenticator: '<EMAIL>' }),
      },
      10000 // 10 second timeout for testing
    );

    console.log('Backend connection test successful');
    console.log('Response status:', response.status);

    return {
      success: true,
      message: 'Backend connection successful',
      details: {
        status: response.status,
        statusText: response.statusText,
        headStatus: headResponse.status,
      }
    };
  } catch (error: any) {
    console.error('Backend connection test failed:', error);

    return {
      success: false,
      message: error.message || 'Backend connection failed',
      details: {
        error: error.message,
        name: error.name,
        stack: error.stack,
      }
    };
  }
};

export const logNetworkInfo = () => {
  console.log('=== Network Configuration ===');
  console.log('API Base URL:', API_CONFIG.BASE_URL);
  console.log('Timeout:', API_CONFIG.TIMEOUT, 'ms');
  console.log('Retry Attempts:', API_CONFIG.RETRY_ATTEMPTS);
  console.log('Retry Delay:', API_CONFIG.RETRY_DELAY, 'ms');
  console.log('============================');
}; 